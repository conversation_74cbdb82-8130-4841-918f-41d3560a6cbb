import json
import os
import requests
import logging
from typing import List, Dict, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class KarlancerScraper:
    def __init__(self, email: str, password: str):
        self.email = email
        self.password = password
        self.session = requests.Session()
        self.cookies_file = "karlancer_cookies.json"
        self.base_url = "https://www.karlancer.com"
        
        # Set default headers
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'en-US,en;q=0.9,fa;q=0.8',
            'Origin': self.base_url,
            'Referer': f'{self.base_url}/',
        })
        
        self.load_cookies()
    
    def load_cookies(self):
        """Load cookies from file if exists"""
        if os.path.exists(self.cookies_file):
            try:
                with open(self.cookies_file, 'r') as f:
                    cookies = json.load(f)
                    self.session.cookies.update(cookies)
                logger.info("Loaded cookies from file")
            except Exception as e:
                logger.error(f"Error loading cookies: {e}")
    
    def save_cookies(self):
        """Save current session cookies to file"""
        try:
            with open(self.cookies_file, 'w') as f:
                json.dump(dict(self.session.cookies), f)
            logger.info("Saved cookies to file")
        except Exception as e:
            logger.error(f"Error saving cookies: {e}")
    
    async def login(self) -> bool:
        """Login to Karlancer and save cookies"""
        try:
            login_data = {
                "identifier": self.email,
                "password": self.password,
                "referrer_code": None,
                "referrer_token": None,
                "unregistered_project_token": None,
                "unregistered_service_token": None,
                "submit_bid_token": None,
                "role": ""
            }
            
            response = self.session.post(
                f"{self.base_url}/api/login",
                json=login_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                self.save_cookies()
                logger.info("Successfully logged in to Karlancer")
                return True
            else:
                logger.error(f"Login failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Login error: {e}")
            return False
    
    async def get_latest_projects(self) -> List[Dict]:
        """Get latest projects from Karlancer"""
        try:
            # Try to get projects first
            response = self.session.get(f"{self.base_url}/search?page=1")
            
            # If unauthorized, try to login
            if response.status_code == 401:
                logger.info("Session expired, attempting to login...")
                if await self.login():
                    response = self.session.get(f"{self.base_url}/search?page=1")
                else:
                    logger.error("Failed to login")
                    return []
            
            if response.status_code == 200:
                # Parse the response and extract project data
                # This will need to be adjusted based on the actual API response
                projects = self.parse_projects(response.text)
                return projects
            else:
                logger.error(f"Failed to get projects: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"Error getting projects: {e}")
            return []
    
    def parse_projects(self, html_content: str) -> List[Dict]:
        """Parse projects from HTML content"""
        # This is a placeholder - you'll need to implement actual parsing
        # based on the HTML structure of the search results
        projects = []
        
        # TODO: Implement HTML parsing to extract:
        # - Project title
        # - Project description
        # - Budget
        # - Deadline
        # - Client info
        # - Project URL
        
        return projects