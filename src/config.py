import os
from dataclasses import dataclass

@dataclass
class Config:
    # Telegram settings
    telegram_token: str = os.getenv("TELEGRAM_TOKEN", "")
    telegram_channel_id: str = os.getenv("TELEGRAM_CHANNEL_ID", "")
    
    # Karlancer settings
    karlancer_email: str = os.getenv("KARLANCER_EMAIL", "")
    karlancer_password: str = os.getenv("KARLANCER_PASSWORD", "")
    
    # Scraping settings
    scrape_interval: int = int(os.getenv("SCRAPE_INTERVAL", "10"))  # minutes
    
    # Cookie storage
    cookies_file: str = "cookies.json"