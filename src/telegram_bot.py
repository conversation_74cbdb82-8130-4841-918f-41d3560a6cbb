import logging
from typing import List, Dict
from pyrogram import Client

logger = logging.getLogger(__name__)

class TelegramBot:
    def __init__(self, token: str, channel_id: str):
        self.client = Client("freelance_scraper", bot_token=token)
        self.channel_id = channel_id
    
    async def send_projects(self, projects: List[Dict], source: str):
        """Send projects to Telegram channel"""
        try:
            async with self.client:
                for project in projects:
                    message = self.format_project_message(project, source)
                    await self.client.send_message(
                        chat_id=self.channel_id,
                        text=message,
                        parse_mode="html"
                    )
        except Exception as e:
            logger.error(f"Error sending to Telegram: {e}")
    
    def format_project_message(self, project: Dict, source: str) -> str:
        """Format project data into a Telegram message"""
        title = project.get('title', 'No title')
        description = project.get('description', 'No description')[:200] + "..."
        budget = project.get('budget', 'Not specified')
        deadline = project.get('deadline', 'Not specified')
        url = project.get('url', '')
        
        message = f"""
🆕 <b>New Project from {source}</b>

📋 <b>Title:</b> {title}
💰 <b>Budget:</b> {budget}
⏰ <b>Deadline:</b> {deadline}

📝 <b>Description:</b>
{description}

🔗 <a href="{url}">View Project</a>
        """.strip()
        
        return message
