import asyncio
import logging
from src.scrapers.karlancer import Karlancer<PERSON>crap<PERSON>
from src.telegram_bot import TelegramBot
from src.config import Config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def main():
    config = Config()
    telegram_bot = TelegramBot(config.telegram_token, config.telegram_channel_id)
    karlancer = KarlancerScraper(config.karlancer_email, config.karlancer_password)
    
    logger.info("Starting freelancer scraper...")
    
    while True:
        try:
            # Scrape Karlancer
            projects = await karlancer.get_latest_projects()
            if projects:
                await telegram_bot.send_projects(projects, "Karlancer")
                logger.info(f"Sent {len(projects)} projects from Karlancer")
            
            # Wait for next iteration
            await asyncio.sleep(config.scrape_interval * 60)  # Convert minutes to seconds
            
        except Exception as e:
            logger.error(f"Error in main loop: {e}")
            await asyncio.sleep(60)  # Wait 1 minute before retrying

if __name__ == "__main__":
    asyncio.run(main())
