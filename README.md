# Freelancer Sites Scraper

A Python-based automated scraper that monitors freelancer platforms for new projects and posts them to a Telegram channel in real-time.

## 🎯 Project Overview

This application continuously scrapes freelancer websites (starting with Karlancer.com) to find new project listings and automatically forwards them to a designated Telegram channel. It's designed to help freelancers stay updated with the latest opportunities without manually checking multiple platforms.

## 🏗️ Architecture

### Core Components

1. **Main Loop** (`main.py`)
   - Orchestrates the scraping process
   - Runs continuously with configurable intervals
   - Handles error recovery and logging

2. **Scrapers** (`src/scrapers/`)
   - Modular scraper classes for different freelancer sites
   - Each scraper handles authentication, session management, and data extraction
   - Currently implemented: `KarlancerScraper`

3. **Telegram Bot** (`src/telegram_bot.py`)
   - Handles message formatting and delivery to Telegram channels
   - Uses kurigram library (Pyrogram fork) for Telegram API interaction

4. **Configuration** (`src/config.py`)
   - Centralized configuration management using environment variables
   - Handles credentials, intervals, and other settings

### Data Flow

```
Freelancer Site → Scraper → Project Data → Telegram Bot → Telegram Channel
```

## 🔧 Technical Implementation

### Session Management
- Uses `requests.Session` for persistent HTTP connections
- Automatic cookie storage and loading for authentication persistence
- Auto-relogin when sessions expire

### Authentication Flow
1. Load saved cookies from file
2. Attempt to access protected content
3. If unauthorized (401), perform login
4. Save new session cookies
5. Retry the original request

### Error Handling
- Graceful handling of network failures
- Automatic retry mechanisms
- Comprehensive logging for debugging

## 🚀 Getting Started

### Prerequisites
- Python 3.13+
- Telegram Bot Token
- Freelancer site credentials

### Installation
```bash
# Clone and setup
git clone <repository>
cd freelancescraper

# Install dependencies
uv sync

# Configure environment
cp .env.example .env
# Edit .env with your credentials
```

### Configuration
Set these environment variables in `.env`:

```env
# Telegram Bot Configuration
TELEGRAM_TOKEN=your_bot_token_here
TELEGRAM_CHANNEL_ID=@your_channel_username

# Karlancer Configuration
KARLANCER_EMAIL=<EMAIL>
KARLANCER_PASSWORD=your_password_here

# Scraping Configuration
SCRAPE_INTERVAL=10  # minutes between scrapes
```

### Running
```bash
python main.py
```

## 📁 Project Structure

```
freelancescraper/
├── main.py                 # Main application entry point
├── src/
│   ├── __init__.py
│   ├── config.py          # Configuration management
│   ├── telegram_bot.py    # Telegram integration
│   └── scrapers/
│       ├── __init__.py
│       └── karlancer.py   # Karlancer.com scraper
├── pyproject.toml         # Dependencies and project metadata
├── .env.example          # Environment variables template
├── .gitignore
└── README.md
```

## 🔌 Adding New Scrapers

To add support for a new freelancer site:

1. **Create a new scraper class** in `src/scrapers/new_site.py`:
```python
class NewSiteScraper:
    def __init__(self, credentials):
        # Initialize session, headers, etc.
        pass
    
    async def login(self) -> bool:
        # Implement login logic
        pass
    
    async def get_latest_projects(self) -> List[Dict]:
        # Implement project extraction
        pass
    
    def parse_projects(self, content: str) -> List[Dict]:
        # Parse HTML/JSON response
        pass
```

2. **Add configuration** in `src/config.py`:
```python
# New site settings
new_site_email: str = os.getenv("NEW_SITE_EMAIL", "")
new_site_password: str = os.getenv("NEW_SITE_PASSWORD", "")
```

3. **Integrate in main loop** (`main.py`):
```python
new_site = NewSiteScraper(config.new_site_email, config.new_site_password)
projects = await new_site.get_latest_projects()
if projects:
    await telegram_bot.send_projects(projects, "NewSite")
```

## 📊 Project Data Format

Each scraper should return projects in this standardized format:

```python
{
    'title': str,           # Project title
    'description': str,     # Project description
    'budget': str,          # Budget information
    'deadline': str,        # Project deadline
    'url': str,            # Direct link to project
    'client': str,         # Client information (optional)
    'skills': List[str],   # Required skills (optional)
    'posted_date': str,    # When project was posted (optional)
}
```

## 🛠️ Development Guidelines

### For AI Assistants Editing This Project

1. **Maintain Modularity**: Keep scrapers independent and follow the established pattern
2. **Error Handling**: Always implement proper exception handling and logging
3. **Session Management**: Use the cookie-based authentication pattern for consistency
4. **Data Validation**: Ensure scraped data matches the expected format
5. **Rate Limiting**: Be respectful of target sites' resources
6. **Testing**: Test login and scraping functionality before deployment

### Key Files to Understand

- `src/scrapers/karlancer.py` - Reference implementation for new scrapers
- `src/telegram_bot.py` - Message formatting and delivery
- `main.py` - Application orchestration and error handling
- `src/config.py` - Configuration management pattern

### Common Tasks

- **Adding new sites**: Follow the scraper pattern in `karlancer.py`
- **Modifying message format**: Edit `format_project_message()` in `telegram_bot.py`
- **Changing scrape intervals**: Update `SCRAPE_INTERVAL` environment variable
- **Adding new data fields**: Update the project data format and message template

## 📝 Dependencies

- **kurigram**: Telegram Bot API (Pyrogram fork)
- **requests**: HTTP client for web scraping
- **beautifulsoup4**: HTML parsing
- **lxml**: XML/HTML parser backend

## 🔒 Security Notes

- Store credentials in environment variables, never in code
- Cookie files are gitignored to prevent credential leakage
- Use secure session management practices
- Respect robots.txt and site terms of service

## 🐛 Troubleshooting

- **Login failures**: Check credentials and site changes
- **Parsing errors**: Inspect HTML structure changes on target sites
- **Telegram errors**: Verify bot token and channel permissions
- **Session issues**: Delete cookie files to force fresh login

## 📈 Future Enhancements

- Database storage for project history
- Duplicate detection and filtering
- Advanced filtering by keywords/budget
- Web dashboard for monitoring
- Support for more freelancer platforms
- Webhook-based real-time notifications

